// Reactive JS Library - Minimal but Powerful

// Signal system for reactivity
let currentEffect = null;
let isBatching = false;
let batchedUpdates = new Set();

function createSignal(initialValue) {
  const subs = new Set();
  let value = initialValue;
  
  const read = () => {
    if (currentEffect) {
      subs.add(currentEffect);
      currentEffect.deps.add(subs);
    }
    return value;
  };
  
  const write = (newValue) => {
    if (typeof newValue === 'function') {
      newValue = newValue(value);
    }
    if (newValue !== value) {
      value = newValue;
      if (isBatching) {
        subs.forEach(fn => batchedUpdates.add(fn));
      } else {
        batch(() => {
          subs.forEach(fn => fn());
        });
      }
    }
  };
  
  return [read, write];
}

function effect(fn) {
  const wrapped = () => {
    cleanup(wrapped);
    currentEffect = wrapped;
    try {
      fn();
    } finally {
      currentEffect = null;
    }
  };
  wrapped.deps = new Set();
  wrapped();
  return wrapped;
}

function cleanup(effectFn) {
  effectFn.deps.forEach(dep => dep.delete(effectFn));
  effectFn.deps.clear();
}

function batch(fn) {
  if (isBatching) {
    fn();
    return;
  }
  
  isBatching = true;
  batchedUpdates.clear();
  
  try {
    fn();
  } finally {
    const updates = Array.from(batchedUpdates);
    batchedUpdates.clear();
    isBatching = false;
    
    updates.forEach(update => update());
  }
}

// JSX-like element creation
function h(tag, props = {}, ...children) {
  // Handle JSX Fragment
  if (tag === Fragment) {
    return children.flat().filter(Boolean);
  }
  
  if (typeof tag === 'function') {
    return tag({ ...props, children: children.flat() });
  }
  
  return {
    tag,
    props: props || {},
    children: children.flat().filter(Boolean)
  };
}

// Fragment symbol for JSX
function Fragment(props) {
  return props.children;
}

// DOM rendering with reactivity
function render(element, container) {
  if (typeof container === 'string') {
    container = document.querySelector(container);
  }
  
  container.innerHTML = '';
  
  const domNode = createElement(element);
  if (domNode) {
    if (domNode.nodeType === 11) { // DocumentFragment
      container.appendChild(domNode);
    } else {
      container.appendChild(domNode);
    }
  }
}

function createElement(element) {
  // Handle null/undefined
  if (element == null || typeof element === 'boolean') {
    return document.createTextNode('');
  }
  
  // Handle text nodes
  if (typeof element === 'string' || typeof element === 'number') {
    return document.createTextNode(String(element));
  }
  
  // Handle arrays (fragments)
  if (Array.isArray(element)) {
    const fragment = document.createDocumentFragment();
    element.forEach(child => {
      const childNode = createElement(child);
      if (childNode) fragment.appendChild(childNode);
    });
    return fragment;
  }
  
  // Handle functions (signals/reactive values)
  if (typeof element === 'function') {
    const textNode = document.createTextNode('');
    effect(() => {
      const value = element();
      textNode.textContent = String(value);
    });
    return textNode;
  }
  
  // Handle component functions
  if (typeof element.tag === 'function') {
    return createElement(element.tag(element.props));
  }
  
  // Handle DOM elements
  const { tag, props, children } = element;
  const domElement = document.createElement(tag);
  
  // Set properties and attributes
  Object.entries(props).forEach(([key, value]) => {
    if (key === 'children') return; // Skip children prop
    
    if (key === 'className' || key === 'class') {
      if (typeof value === 'function') {
        effect(() => {
          domElement.className = value();
        });
      } else {
        domElement.className = value;
      }
    } else if (key.startsWith('on') && typeof value === 'function') {
      // Event handlers
      const eventName = key.slice(2).toLowerCase();
      domElement.addEventListener(eventName, value);
    } else if (key === 'style' && typeof value === 'object') {
      Object.assign(domElement.style, value);
    } else if (key === 'value' && (tag === 'input' || tag === 'textarea')) {
      // Special handling for form inputs
      if (typeof value === 'function') {
        effect(() => {
          domElement.value = value();
        });
      } else {
        domElement.value = value;
      }
    } else if (typeof value === 'function') {
      // Reactive attributes
      effect(() => {
        const val = value();
        if (val != null && val !== false) {
          domElement.setAttribute(key, val === true ? '' : val);
        } else {
          domElement.removeAttribute(key);
        }
      });
    } else if (value != null && value !== false) {
      domElement.setAttribute(key, value === true ? '' : value);
    }
  });
  
  // Append children
  children.forEach(child => {
    if (typeof child === 'function') {
      // Reactive child - create text node and update it
      const textNode = document.createTextNode('');
      domElement.appendChild(textNode);

      effect(() => {
        const value = child();
        textNode.textContent = String(value);
      });
    } else {
      const childNode = createElement(child);
      if (childNode) {
        domElement.appendChild(childNode);
      }
    }
  });
  
  return domElement;
}

// Exports
export { createSignal, h, render, Fragment };